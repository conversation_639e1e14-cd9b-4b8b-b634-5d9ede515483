package com.ehome.web.controller.oc;

import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.IdUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分享统计管理Controller
 */
@Controller
@RequestMapping("/oc/share")
public class ShareController extends BaseController {
    
    private String prefix = "oc/share";

    /**
     * 跳转到分享统计页面
     */
    @GetMapping("/statistics")
    public String statistics(ModelMap mmap) {
        return prefix + "/statistics";
    }

    /**
     * 分享详细记录页面
     */
    @GetMapping("/detail")
    public String detail() {
        return prefix + "/detail";
    }

    /**
     * 获取分享统计数据
     */
    @GetMapping("/data")
    @ResponseBody
    public AjaxResult getStatisticsData(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            // 分享总数统计
            String shareSql = "SELECT COUNT(*) as total_shares, " +
                    "SUM(CASE WHEN share_type = 'app_message' THEN 1 ELSE 0 END) as friend_shares, " +
                    "SUM(CASE WHEN share_type = 'timeline' THEN 1 ELSE 0 END) as timeline_shares " +
                    "FROM eh_wx_share_record WHERE community_id = ? AND create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            Record shareStats = Db.findFirst(shareSql, communityId, days);

            // 访问统计
            String visitSql = "SELECT COUNT(*) as total_visits, COUNT(DISTINCT visitor_openid) as unique_visitors, " +
                    "SUM(is_new_user) as new_users FROM eh_wx_share_visit v " +
                    "JOIN eh_wx_share_record r ON v.share_record_id = r.id " +
                    "WHERE r.community_id = ? AND v.visit_time >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            Record visitStats = Db.findFirst(visitSql, communityId, days);

            // 分享排行榜
            String rankSql = "SELECT r.nickname, COUNT(*) as share_count FROM eh_wx_share_record r " +
                    "WHERE r.community_id = ? AND r.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    "GROUP BY r.user_id, r.nickname ORDER BY share_count DESC LIMIT 10";
            List<Record> shareRankRecords = Db.find(rankSql, communityId, days);

            // 最近分享记录
            String recentSql = "SELECT nickname, share_type, create_time FROM eh_wx_share_record " +
                    "WHERE community_id = ? ORDER BY create_time DESC LIMIT 10";
            List<Record> recentShareRecords = Db.find(recentSql, communityId);

            // 转换Record为Map，避免前端解析问题
            List<Map<String, Object>> shareRank = new ArrayList<>();
            for (Record record : shareRankRecords) {
                shareRank.add(record.toMap());
            }

            List<Map<String, Object>> recentShares = new ArrayList<>();
            for (Record record : recentShareRecords) {
                recentShares.add(record.toMap());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("shareStats", shareStats != null ? shareStats.toMap() : new HashMap<>());
            result.put("visitStats", visitStats != null ? visitStats.toMap() : new HashMap<>());
            result.put("shareRank", shareRank);
            result.put("recentShares", recentShares);

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取分享统计数据失败", e);
            return AjaxResult.error("获取分享统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取详细分享记录列表
     */
    @PostMapping("/list")
    @ResponseBody
    public AjaxResult list(@RequestParam Map<String, Object> params) {
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            int pageNum = Integer.parseInt(params.getOrDefault("pageNum", "1").toString());
            int pageSize = Integer.parseInt(params.getOrDefault("pageSize", "10").toString());
            String shareType = (String) params.get("shareType");
            String shareSource = (String) params.get("shareSource");
            String startTime = (String) params.get("startTime");
            String endTime = (String) params.get("endTime");

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.*, ");
            sql.append("(SELECT COUNT(*) FROM eh_wx_share_visit v WHERE v.share_record_id = r.id) as visit_count ");
            sql.append("FROM eh_wx_share_record r WHERE r.community_id = ?");

            if (StringUtils.isNotEmpty(shareType)) {
                sql.append(" AND r.share_type = '").append(shareType).append("'");
            }
            if (StringUtils.isNotEmpty(shareSource)) {
                sql.append(" AND r.share_source = '").append(shareSource).append("'");
            }
            if (StringUtils.isNotEmpty(startTime)) {
                sql.append(" AND r.create_time >= '").append(startTime).append("'");
            }
            if (StringUtils.isNotEmpty(endTime)) {
                sql.append(" AND r.create_time <= '").append(endTime).append("'");
            }

            sql.append(" ORDER BY r.create_time DESC");

            // 分页查询
            com.jfinal.plugin.activerecord.Page<Record> page = Db.paginate(pageNum, pageSize, 
                "SELECT r.*, (SELECT COUNT(*) FROM eh_wx_share_visit v WHERE v.share_record_id = r.id) as visit_count",
                sql.toString(), communityId);

            Map<String, Object> result = new HashMap<>();
            result.put("rows", page.getList());
            result.put("total", page.getTotalRow());

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取分享记录列表失败", e);
            return AjaxResult.error("获取分享记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除分享记录
     */
    @Log(title = "分享记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        try {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                // 删除访问记录
                Db.delete("DELETE FROM eh_wx_share_visit WHERE share_record_id = ?", id);
                // 删除分享记录
                Db.delete("DELETE FROM eh_wx_share_record WHERE id = ?", id);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("删除分享记录失败", e);
            return AjaxResult.error("删除分享记录失败: " + e.getMessage());
        }
    }

    /**
     * 手动更新分享统计数据
     */
    @PostMapping("/updateStatistics")
    @ResponseBody
    public AjaxResult updateStatistics() {
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            // 获取所有有效的分享记录ID
            List<Record> shareRecords = Db.find(
                    "SELECT DISTINCT share_record_id FROM eh_wx_share_visit WHERE share_record_id NOT LIKE 'unknown_%'"
            );

            int updateCount = 0;
            for (Record record : shareRecords) {
                String shareRecordId = record.getStr("share_record_id");
                if (updateShareStatistics(shareRecordId)) {
                    updateCount++;
                }
            }

            return AjaxResult.success("成功更新 " + updateCount + " 条统计记录");

        } catch (Exception e) {
            logger.error("更新分享统计失败", e);
            return AjaxResult.error("更新分享统计失败: " + e.getMessage());
        }
    }

    /**
     * 更新单个分享记录的统计数据
     */
    private boolean updateShareStatistics(String shareRecordId) {
        try {
            if (shareRecordId == null || shareRecordId.startsWith("unknown_")) {
                return false;
            }

            // 计算统计数据
            String sql = "SELECT " +
                    "COUNT(*) as total_visits, " +
                    "COUNT(DISTINCT visitor_openid) as unique_visitors, " +
                    "SUM(is_new_user) as new_users, " +
                    "SUM(stay_duration) as total_stay_duration, " +
                    "AVG(stay_duration) as avg_stay_duration, " +
                    "MAX(visit_time) as last_visit_time " +
                    "FROM eh_wx_share_visit WHERE share_record_id = ?";

            Record stats = Db.findFirst(sql, shareRecordId);
            if (stats == null) {
                return false;
            }

            // 插入或更新统计记录
            String insertSql = "INSERT INTO eh_wx_share_statistics (" +
                    "id, share_record_id, total_visits, unique_visitors, new_users, " +
                    "total_stay_duration, avg_stay_duration, last_visit_time, update_time" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "total_visits = VALUES(total_visits), " +
                    "unique_visitors = VALUES(unique_visitors), " +
                    "new_users = VALUES(new_users), " +
                    "total_stay_duration = VALUES(total_stay_duration), " +
                    "avg_stay_duration = VALUES(avg_stay_duration), " +
                    "last_visit_time = VALUES(last_visit_time), " +
                    "update_time = VALUES(update_time)";

            Db.update(insertSql,
                    IdUtils.fastUUID(),
                    shareRecordId,
                    stats.getInt("total_visits"),
                    stats.getInt("unique_visitors"),
                    stats.getInt("new_users"),
                    stats.getInt("total_stay_duration"),
                    stats.getBigDecimal("avg_stay_duration"),
                    stats.getDate("last_visit_time"),
                    new java.util.Date()
            );

            return true;

        } catch (Exception e) {
            logger.error("更新分享统计失败: shareRecordId = {}", shareRecordId, e);
            return false;
        }
    }

    /**
     * 获取分享详细记录列表
     */
    @GetMapping("/detailList")
    @ResponseBody
    public AjaxResult getDetailList(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "sharerName", required = false) String sharerName,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime) {
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("社区信息不存在");
            }

            // 构建查询条件
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT r.id, r.nickname as sharer_name, r.share_title, r.share_desc, ");
            sql.append("r.share_source, r.create_time as share_time ");
            sql.append("FROM eh_wx_share_record r ");
            sql.append("WHERE r.community_id = ? ");

            List<Object> params = new ArrayList<>();
            params.add(communityId);

            if (StringUtils.isNotEmpty(sharerName)) {
                sql.append("AND r.nickname LIKE ? ");
                params.add("%" + sharerName + "%");
            }
            if (StringUtils.isNotEmpty(startTime)) {
                sql.append("AND r.create_time >= ? ");
                params.add(startTime);
            }
            if (StringUtils.isNotEmpty(endTime)) {
                sql.append("AND r.create_time <= ? ");
                params.add(endTime);
            }

            sql.append("ORDER BY r.create_time DESC ");

            // 分页查询
            com.jfinal.plugin.activerecord.Page<Record> pageResult = Db.paginate(
                    page, pageSize,
                    "SELECT r.id, r.nickname as sharer_name, r.share_title, r.share_desc, r.share_source, r.create_time as share_time",
                    sql.toString().replace("SELECT r.id, r.nickname as sharer_name, r.share_title, r.share_desc, r.share_source, r.create_time as share_time FROM eh_wx_share_record r ", "FROM eh_wx_share_record r "),
                    params.toArray()
            );

            List<Record> records = pageResult.getList();
            List<Map<String, Object>> result = new ArrayList<>();

            // 为每个分享记录获取访问者信息
            for (Record record : records) {
                Map<String, Object> shareRecord = record.toMap();
                String shareId = record.getStr("id");

                // 获取访问统计
                Record visitStats = Db.findFirst(
                        "SELECT COUNT(*) as visit_count, COUNT(DISTINCT visitor_openid) as unique_visitors " +
                        "FROM eh_wx_share_visit WHERE share_record_id = ?", shareId);
                if (visitStats != null) {
                    shareRecord.put("visit_count", visitStats.getInt("visit_count"));
                    shareRecord.put("unique_visitors", visitStats.getInt("unique_visitors"));
                } else {
                    shareRecord.put("visit_count", 0);
                    shareRecord.put("unique_visitors", 0);
                }

                // 获取访问者列表
                List<Record> visitors = Db.find(
                        "SELECT visitor_nickname, visit_time FROM eh_wx_share_visit " +
                        "WHERE share_record_id = ? ORDER BY visit_time DESC LIMIT 10", shareId);
                List<Map<String, Object>> visitorList = new ArrayList<>();
                for (Record visitor : visitors) {
                    visitorList.add(visitor.toMap());
                }
                shareRecord.put("visitors", visitorList);

                result.add(shareRecord);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("records", result);
            data.put("total", pageResult.getTotalRow());
            data.put("page", page);
            data.put("pageSize", pageSize);

            return AjaxResult.success(data);

        } catch (Exception e) {
            logger.error("获取分享详细记录失败", e);
            return AjaxResult.error("获取分享详细记录失败: " + e.getMessage());
        }
    }
}
